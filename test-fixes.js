// Test script to verify the fixes are working
// Run this in the browser console on the appointments page

console.log('🧪 TESTING AGENSALUD FIXES');
console.log('========================');

// Test 1: Check current date and time
console.log('\n📅 Test 1: Date/Time Verification');
const now = new Date();
console.log('Current time:', now.toISOString());
console.log('Local time:', now.toLocaleString());
console.log('Timezone:', Intl.DateTimeFormat().resolvedOptions().timeZone);

// Test 2: Check for past appointments in DOM
console.log('\n🔍 Test 2: DOM Analysis');
const appointmentCards = document.querySelectorAll('[class*="appointment"], .bg-white.border');
console.log(`Found ${appointmentCards.length} potential appointment cards`);

// Look for date elements
const dateElements = document.querySelectorAll('[class*="date"], [class*="time"]');
console.log(`Found ${dateElements.length} date/time elements`);

// Check for May 31, 2025 appointments
let mayAppointments = 0;
dateElements.forEach(el => {
  if (el.textContent && el.textContent.includes('31 de mayo')) {
    mayAppointments++;
    console.log('Found May 31 appointment:', el.textContent);
  }
});
console.log(`Found ${mayAppointments} May 31, 2025 appointments in DOM`);

// Test 3: Check for action buttons on past appointments
console.log('\n🔘 Test 3: Action Button Analysis');
const actionButtons = document.querySelectorAll('button[class*="blue"], button[class*="red"]');
console.log(`Found ${actionButtons.length} action buttons`);

actionButtons.forEach((button, index) => {
  const buttonText = button.textContent?.trim();
  if (buttonText && (buttonText.includes('Reagendar') || buttonText.includes('Cancelar'))) {
    console.log(`Action button ${index + 1}: "${buttonText}"`);
    
    // Check if this button is near a May 31 date
    const parentCard = button.closest('.bg-white, [class*="appointment"]');
    if (parentCard) {
      const cardText = parentCard.textContent;
      if (cardText && cardText.includes('31 de mayo')) {
        console.warn('⚠️ ISSUE: Found action button on May 31 appointment!');
      }
    }
  }
});

// Test 4: Check tabs
console.log('\n📑 Test 4: Tab Analysis');
const tabs = document.querySelectorAll('[role="tab"], [aria-selected]');
console.log(`Found ${tabs.length} tabs`);

tabs.forEach((tab, index) => {
  const isSelected = tab.getAttribute('aria-selected') === 'true' || tab.classList.contains('border-blue-500');
  const tabText = tab.textContent?.trim();
  console.log(`Tab ${index + 1}: "${tabText}" (selected: ${isSelected})`);
});

// Test 5: Check for debug button
console.log('\n🔧 Test 5: Debug Tools');
const debugButton = document.querySelector('button[title*="Force refresh"]');
if (debugButton) {
  console.log('✅ Debug refresh button found');
} else {
  console.log('❌ Debug refresh button not found (may be production mode)');
}

// Test 6: Navigation test
console.log('\n🧭 Test 6: Navigation Elements');
const citasLinks = document.querySelectorAll('a[href*="appointments"], button[onclick*="appointments"]');
console.log(`Found ${citasLinks.length} appointment navigation elements`);

citasLinks.forEach((link, index) => {
  const href = link.getAttribute('href') || 'button';
  const text = link.textContent?.trim();
  console.log(`Navigation ${index + 1}: "${text}" → ${href}`);
});

// Test 7: Console error check
console.log('\n❌ Test 7: Error Check');
console.log('Check above for any red error messages in console');

// Test 8: Local storage check
console.log('\n💾 Test 8: Storage Analysis');
const localStorageKeys = Object.keys(localStorage).filter(key => 
  key.includes('appointment') || key.includes('supabase')
);
console.log('Relevant localStorage keys:', localStorageKeys);

// Summary
console.log('\n📊 TEST SUMMARY');
console.log('===============');
console.log(`✅ Current time: ${now.toLocaleString()}`);
console.log(`📋 Appointment cards found: ${appointmentCards.length}`);
console.log(`📅 May 31 appointments: ${mayAppointments}`);
console.log(`🔘 Action buttons: ${actionButtons.length}`);
console.log(`📑 Tabs: ${tabs.length}`);
console.log(`🧭 Navigation links: ${citasLinks.length}`);

console.log('\n🎯 EXPECTED RESULTS:');
console.log('- May 31 appointments should be in "Historial" tab');
console.log('- No "Reagendar" or "Cancelar" buttons on May 31 appointments');
console.log('- All navigation links should work without infinite loading');
console.log('- Debug button should be visible in development mode');

console.log('\n✅ Test completed! Check results above.');
