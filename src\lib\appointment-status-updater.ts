/**
 * Appointment Status Updater
 * 
 * Utility functions to automatically update past appointments to appropriate statuses
 * and ensure proper filtering in the frontend.
 * 
 * <AUTHOR> MVP Team
 * @version 1.0.0
 */

import { createClient } from '@/lib/supabase/client';

export interface AppointmentStatusUpdate {
  id: string;
  oldStatus: string;
  newStatus: string;
  appointmentDate: string;
  startTime: string;
}

/**
 * Updates past appointments that are still marked as 'confirmed' or 'pending' to 'no-show'
 * This should be called when loading appointments to ensure data consistency
 */
export async function updatePastAppointmentsToNoShow(organizationId: string): Promise<{
  success: boolean;
  updatedCount: number;
  updates: AppointmentStatusUpdate[];
  error?: string;
}> {
  try {
    const supabase = createClient();
    const now = new Date();
    
    // Get all past appointments that are still marked as confirmed or pending
    const { data: pastAppointments, error: fetchError } = await supabase
      .from('appointments')
      .select('id, appointment_date, start_time, status')
      .eq('organization_id', organizationId)
      .in('status', ['confirmed', 'pending'])
      .lt('appointment_date', now.toISOString().split('T')[0]); // Past dates
    
    if (fetchError) {
      console.error('Error fetching past appointments:', fetchError);
      return { success: false, updatedCount: 0, updates: [], error: fetchError.message };
    }

    if (!pastAppointments || pastAppointments.length === 0) {
      return { success: true, updatedCount: 0, updates: [] };
    }

    // Filter appointments that are actually in the past (including time)
    const appointmentsToUpdate = pastAppointments.filter(appointment => {
      const appointmentDateTime = new Date(`${appointment.appointment_date}T${appointment.start_time}`);
      return appointmentDateTime <= now;
    });

    if (appointmentsToUpdate.length === 0) {
      return { success: true, updatedCount: 0, updates: [] };
    }

    console.log(`🔄 Updating ${appointmentsToUpdate.length} past appointments to no-show status`);

    // Update appointments to no-show status
    const updates: AppointmentStatusUpdate[] = [];
    const updatePromises = appointmentsToUpdate.map(async (appointment) => {
      const { error: updateError } = await supabase
        .from('appointments')
        .update({ 
          status: 'no-show',
          updated_at: new Date().toISOString()
        })
        .eq('id', appointment.id);

      if (updateError) {
        console.error(`Error updating appointment ${appointment.id}:`, updateError);
        return null;
      }

      const update: AppointmentStatusUpdate = {
        id: appointment.id,
        oldStatus: appointment.status,
        newStatus: 'no-show',
        appointmentDate: appointment.appointment_date,
        startTime: appointment.start_time
      };
      updates.push(update);
      return update;
    });

    await Promise.all(updatePromises);

    console.log(`✅ Successfully updated ${updates.length} appointments to no-show status`);
    
    return {
      success: true,
      updatedCount: updates.length,
      updates
    };

  } catch (error) {
    console.error('Error in updatePastAppointmentsToNoShow:', error);
    return {
      success: false,
      updatedCount: 0,
      updates: [],
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Validates appointment filtering logic on the frontend
 * Returns detailed analysis of appointment categorization
 */
export function validateAppointmentFiltering(appointments: any[]): {
  vigentesCount: number;
  historialCount: number;
  issues: Array<{
    appointmentId: string;
    issue: string;
    expectedCategory: 'vigentes' | 'historial';
    actualCategory: 'vigentes' | 'historial';
  }>;
} {
  const now = new Date();
  const issues: Array<{
    appointmentId: string;
    issue: string;
    expectedCategory: 'vigentes' | 'historial';
    actualCategory: 'vigentes' | 'historial';
  }> = [];

  let vigentesCount = 0;
  let historialCount = 0;

  appointments.forEach(appointment => {
    const appointmentDate = new Date(`${appointment.appointment_date}T${appointment.start_time}`);
    const isFuture = appointmentDate > now;
    const isActiveStatus = ['confirmed', 'pending'].includes(appointment.status);
    const isHistoryStatus = ['completed', 'cancelled', 'no-show'].includes(appointment.status);

    const shouldBeVigente = isFuture && isActiveStatus;
    const shouldBeHistorial = !isFuture || isHistoryStatus;

    if (shouldBeVigente) {
      vigentesCount++;
    } else if (shouldBeHistorial) {
      historialCount++;
    }

    // Check for issues
    if (!isFuture && isActiveStatus) {
      issues.push({
        appointmentId: appointment.id,
        issue: 'Past appointment with active status (should be no-show)',
        expectedCategory: 'historial',
        actualCategory: 'vigentes'
      });
    }

    if (isFuture && isHistoryStatus) {
      issues.push({
        appointmentId: appointment.id,
        issue: 'Future appointment with history status',
        expectedCategory: 'vigentes',
        actualCategory: 'historial'
      });
    }
  });

  return {
    vigentesCount,
    historialCount,
    issues
  };
}

/**
 * Force refresh appointment data by clearing any cached state
 */
export function clearAppointmentCache(): void {
  // Clear any localStorage cache
  const keys = Object.keys(localStorage);
  keys.forEach(key => {
    if (key.includes('appointment') || key.includes('supabase-auth')) {
      localStorage.removeItem(key);
    }
  });

  // Clear sessionStorage cache
  const sessionKeys = Object.keys(sessionStorage);
  sessionKeys.forEach(key => {
    if (key.includes('appointment')) {
      sessionStorage.removeItem(key);
    }
  });

  console.log('🧹 Cleared appointment cache');
}
