// Debug script to run in browser console to check appointment filtering
// Copy and paste this into the browser console on the appointments page

console.log('=== FRONTEND APPOINTMENT DEBUG ===');

// Check if we can access the appointments data
if (window.React) {
  console.log('React is available');
} else {
  console.log('React not found in window');
}

// Try to find appointment data in the DOM
const appointmentCards = document.querySelectorAll('[data-testid="appointment-card"], .appointment-card, [class*="appointment"]');
console.log('Found appointment elements:', appointmentCards.length);

// Check for tab elements
const tabs = document.querySelectorAll('[role="tab"], [aria-selected]');
console.log('Found tab elements:', tabs.length);
tabs.forEach((tab, index) => {
  console.log(`Tab ${index}:`, {
    text: tab.textContent,
    selected: tab.getAttribute('aria-selected'),
    id: tab.id
  });
});

// Check for date elements
const dateElements = document.querySelectorAll('[class*="date"], [class*="time"]');
console.log('Found date/time elements:', dateElements.length);

// Look for appointment data in React DevTools if available
if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
  console.log('React DevTools detected');
} else {
  console.log('React DevTools not available');
}

// Check current URL and search params
console.log('Current URL:', window.location.href);
console.log('Search params:', new URLSearchParams(window.location.search).toString());

// Check for any JavaScript errors
console.log('Console errors check - look above for any red error messages');

// Check local storage for any cached data
const localStorageKeys = Object.keys(localStorage);
console.log('LocalStorage keys:', localStorageKeys);
localStorageKeys.forEach(key => {
  if (key.includes('appointment') || key.includes('supabase')) {
    console.log(`${key}:`, localStorage.getItem(key));
  }
});

// Check session storage
const sessionStorageKeys = Object.keys(sessionStorage);
console.log('SessionStorage keys:', sessionStorageKeys);
sessionStorageKeys.forEach(key => {
  if (key.includes('appointment') || key.includes('supabase')) {
    console.log(`${key}:`, sessionStorage.getItem(key));
  }
});

console.log('=== END DEBUG ===');
