// Debug script to understand the date comparison issue
// Run this to see how the date comparison works

console.log('=== DATE COMPARISON DEBUG ===');
console.log('Current Date/Time:', new Date());
console.log('Current Date ISO:', new Date().toISOString());
console.log('Current Date Local:', new Date().toLocaleString());
console.log('Timezone Offset (minutes):', new Date().getTimezoneOffset());

// Simulate the appointment data from the screenshot
const mockAppointments = [
  {
    appointment_date: '2025-05-31',
    start_time: '09:00:00',
    status: 'confirmed'
  },
  {
    appointment_date: '2025-05-31',
    start_time: '10:00:00', 
    status: 'confirmed'
  },
  {
    appointment_date: '2025-05-31',
    start_time: '11:00:00',
    status: 'confirmed'
  }
];

console.log('\n=== APPOINTMENT ANALYSIS ===');

mockAppointments.forEach((appointment, index) => {
  console.log(`\nAppointment ${index + 1}:`);
  console.log(`  Date: ${appointment.appointment_date}`);
  console.log(`  Time: ${appointment.start_time}`);
  console.log(`  Status: ${appointment.status}`);
  
  // Recreate the logic from AppointmentTabs.tsx
  const appointmentDate = new Date(`${appointment.appointment_date}T${appointment.start_time}`);
  const now = new Date();
  const isFuture = appointmentDate > now;
  const isActiveStatus = ['confirmed', 'pending'].includes(appointment.status);
  
  console.log(`  Appointment DateTime: ${appointmentDate}`);
  console.log(`  Current DateTime: ${now}`);
  console.log(`  Is Future: ${isFuture}`);
  console.log(`  Is Active Status: ${isActiveStatus}`);
  console.log(`  Should be Vigente: ${isFuture && isActiveStatus}`);
  console.log(`  Should be Historial: ${!isFuture || ['completed', 'cancelled'].includes(appointment.status)}`);
});

console.log('\n=== TIMEZONE CONSIDERATIONS ===');
console.log('Local timezone:', Intl.DateTimeFormat().resolvedOptions().timeZone);

// Test with explicit timezone
const testDate = new Date('2025-05-31T09:00:00');
console.log('Test date (no timezone):', testDate);
console.log('Test date ISO:', testDate.toISOString());
console.log('Test date local:', testDate.toLocaleString());

// Test with timezone
const testDateWithTZ = new Date('2025-05-31T09:00:00-05:00');
console.log('Test date (with -05:00 TZ):', testDateWithTZ);
console.log('Test date with TZ ISO:', testDateWithTZ.toISOString());
